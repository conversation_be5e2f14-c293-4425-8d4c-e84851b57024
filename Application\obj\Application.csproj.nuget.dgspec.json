{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[13.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[13.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}